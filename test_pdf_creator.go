package main

import (
	"fmt"
	"log"

	"github.com/unidoc/unipdf/v3/creator"
)

func main() {
	// 创建几个测试PDF文件
	for i := 1; i <= 3; i++ {
		createTestPDF(fmt.Sprintf("test_invoice_%d.pdf", i), fmt.Sprintf("测试发票 #%d", i))
	}
	fmt.Println("测试PDF文件创建完成")
}

func createTestPDF(filename, title string) {
	c := creator.New()
	c.SetPageMargins(50, 50, 50, 50)

	// 添加标题
	titlePara := c.NewParagraph(title)
	titlePara.SetFontSize(24)
	titlePara.SetMargins(0, 0, 20, 20)
	c.Draw(titlePara)

	// 添加发票内容
	content := fmt.Sprintf(`
发票号码: INV-%s-001
开票日期: 2024-01-15
购买方: 测试公司
销售方: 示例企业

商品明细:
- 商品A  数量: 2  单价: ¥100.00  金额: ¥200.00
- 商品B  数量: 1  单价: ¥150.00  金额: ¥150.00

合计金额: ¥350.00
税额: ¥45.50
价税合计: ¥395.50

备注: 这是一个测试发票，用于演示PDF合并功能。
`, filename[:len(filename)-4])

	contentPara := c.NewParagraph(content)
	contentPara.SetFontSize(12)
	contentPara.SetMargins(0, 0, 10, 10)
	c.Draw(contentPara)

	// 保存文件
	err := c.WriteToFile(filename)
	if err != nil {
		log.Printf("Error creating %s: %v", filename, err)
	}
}
