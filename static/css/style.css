* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
}

.logo h1 {
    color: #4CAF50;
    font-size: 2.5em;
    font-weight: bold;
}

.subtitle {
    color: #666;
    font-size: 1.1em;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.feature {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature img {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.feature h3 {
    color: #4CAF50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.feature p {
    color: #666;
    line-height: 1.8;
}

.upload-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #4CAF50;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.upload-area:hover {
    border-color: #45a049;
    background-color: rgba(76, 175, 80, 0.05);
}

.upload-area.dragover {
    border-color: #45a049;
    background-color: rgba(76, 175, 80, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.upload-text {
    font-size: 1.3em;
    color: #4CAF50;
    margin-bottom: 10px;
    font-weight: bold;
}

.upload-subtext {
    color: #666;
    font-size: 1em;
}

.layout-options {
    margin-bottom: 30px;
}

.layout-options h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.layout-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.layout-btn {
    padding: 12px 24px;
    border: 2px solid #4CAF50;
    background: white;
    color: #4CAF50;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    transition: all 0.3s ease;
}

.layout-btn:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
}

.layout-btn.active {
    background: #4CAF50;
    color: white;
}

.file-list {
    margin-top: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 10px;
}

.file-list h3 {
    color: #333;
    margin-bottom: 15px;
}

.file-list ul {
    list-style: none;
    margin-bottom: 20px;
}

.file-list li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    color: #666;
}

.merge-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.merge-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.progress {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.result {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 30px;
}

.result h3 {
    color: #4CAF50;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.download-btn {
    display: inline-block;
    background: #4CAF50;
    color: white;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 40px;
}

.footer p {
    margin-bottom: 10px;
}

.tip {
    font-size: 0.9em;
    font-style: italic;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        padding: 20px;
    }
    
    .logo h1 {
        font-size: 2em;
    }
    
    .features {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .upload-section {
        padding: 20px;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
    
    .layout-buttons {
        justify-content: center;
    }
}
