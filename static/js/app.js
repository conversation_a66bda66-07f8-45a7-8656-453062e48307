class InvoiceMerger {
    constructor() {
        this.selectedFiles = [];
        this.currentLayout = 2;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const layoutButtons = document.querySelectorAll('.layout-btn');
        const mergeBtn = document.getElementById('mergeBtn');

        // 上传区域点击事件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });

        // 布局按钮事件
        layoutButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                layoutButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentLayout = parseInt(btn.dataset.layout);
            });
        });

        // 合并按钮事件
        mergeBtn.addEventListener('click', () => {
            this.mergePDFs();
        });
    }

    handleFiles(files) {
        const validFiles = Array.from(files).filter(file => {
            if (file.type !== 'application/pdf') {
                this.showError('只支持PDF文件格式');
                return false;
            }
            return true;
        });

        if (validFiles.length === 0) {
            return;
        }

        this.selectedFiles = validFiles;
        this.displaySelectedFiles();
    }

    displaySelectedFiles() {
        const fileList = document.getElementById('fileList');
        const selectedFilesList = document.getElementById('selectedFiles');

        selectedFilesList.innerHTML = '';
        this.selectedFiles.forEach((file, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <span>${file.name} (${this.formatFileSize(file.size)})</span>
                <button onclick="invoiceMerger.removeFile(${index})" style="margin-left: 10px; color: red; border: none; background: none; cursor: pointer;">删除</button>
            `;
            selectedFilesList.appendChild(li);
        });

        fileList.style.display = 'block';
    }

    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        if (this.selectedFiles.length === 0) {
            document.getElementById('fileList').style.display = 'none';
        } else {
            this.displaySelectedFiles();
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async mergePDFs() {
        if (this.selectedFiles.length === 0) {
            this.showError('请先选择PDF文件');
            return;
        }

        this.showProgress();

        const formData = new FormData();
        this.selectedFiles.forEach(file => {
            formData.append('files', file);
        });
        formData.append('layout', this.currentLayout.toString());

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showResult(result.downloadUrl);
            } else {
                this.showError(result.error || '合并失败');
            }
        } catch (error) {
            this.showError('网络错误：' + error.message);
        }
    }

    showProgress() {
        document.getElementById('progress').style.display = 'block';
        document.getElementById('result').style.display = 'none';
        
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressFill.style.width = progress + '%';
            progressText.textContent = `正在处理... ${Math.round(progress)}%`;
        }, 200);

        // 存储interval ID以便后续清除
        this.progressInterval = interval;
    }

    showResult(downloadUrl) {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressFill.style.width = '100%';
        progressText.textContent = '处理完成！';

        setTimeout(() => {
            document.getElementById('progress').style.display = 'none';
            document.getElementById('result').style.display = 'block';
            
            const downloadLink = document.getElementById('downloadLink');
            downloadLink.href = downloadUrl;
            downloadLink.download = 'merged_invoice.pdf';
        }, 1000);
    }

    showError(message) {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        document.getElementById('progress').style.display = 'none';
        alert('错误：' + message);
    }
}

// 初始化应用
const invoiceMerger = new InvoiceMerger();
