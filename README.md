# PDF电子发票批量合并打印工具

这是一个用Go语言开发的PDF电子发票批量合并打印工具，类似于 https://www.lvfapiao.com/ 的功能。

## 功能特性

- 📄 **PDF文件上传**: 支持拖拽和手动上传多个PDF发票文件
- 🔄 **PDF合并**: 将多个发票PDF合并成一个文件
- 📐 **多种布局**: 支持每页2张、3张、4张发票的打印布局
- 💾 **文件下载**: 合并后自动提供下载链接
- 🌱 **节省纸张**: 通过合并打印减少纸张使用，保护环境
- 📱 **响应式设计**: 支持PC和移动设备访问

## 技术栈

- **后端**: Go + Gin框架
- **前端**: HTML5 + CSS3 + JavaScript
- **PDF处理**: UniPDF库
- **文件上传**: 支持多文件拖拽上传

## 安装和运行

### 前提条件

- Go 1.21 或更高版本
- UniPDF许可证（用于PDF处理功能）

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd invoice-merger
```

2. 安装依赖
```bash
go mod tidy
```

3. 编译项目
```bash
go build -o invoice-merger main.go
```

4. 运行服务器
```bash
./invoice-merger
```

5. 在浏览器中访问 `http://localhost:8081`

## 使用方法

1. **上传PDF文件**
   - 将PDF发票文件拖拽到上传区域
   - 或点击上传区域选择文件
   - 支持同时上传多个PDF文件

2. **选择布局**
   - 每页2张：适合A4纸张，每页放置2张发票
   - 每页3张：更紧凑的布局
   - 每页4张：最大化纸张利用率

3. **合并和下载**
   - 点击"开始合并"按钮
   - 等待处理完成
   - 点击下载链接获取合并后的PDF

## 项目结构

```
.
├── main.go                 # 主程序文件
├── templates/
│   └── index.html          # 主页模板
├── static/
│   ├── css/
│   │   └── style.css       # 样式文件
│   ├── js/
│   │   └── app.js          # 前端JavaScript
│   └── images/             # 图标和图片
├── uploads/                # 临时上传目录
├── output/                 # 输出文件目录
└── README.md              # 项目说明
```

## API接口

### POST /upload
上传并合并PDF文件

**参数:**
- `files`: 多个PDF文件
- `layout`: 布局类型 (2, 3, 或 4)

**响应:**
```json
{
  "success": true,
  "downloadUrl": "/download/merged_xxx.pdf"
}
```

### GET /download/:filename
下载合并后的PDF文件

## 环境变量

- `GIN_MODE`: 设置为 `release` 用于生产环境

## 注意事项

1. **UniPDF许可证**: 本项目使用UniPDF库处理PDF文件，商业使用需要购买许可证
2. **文件安全**: 上传的文件会在处理后自动删除
3. **文件大小**: 建议单个PDF文件不超过10MB
4. **浏览器兼容性**: 推荐使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 开发计划

- [ ] 添加更多布局选项
- [ ] 支持PDF密码保护
- [ ] 添加文件预览功能
- [ ] 优化移动端体验
- [ ] 添加批量处理队列

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
