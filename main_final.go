package main

import (
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type InvoiceMerger struct {
	uploadDir string
	outputDir string
}

func NewInvoiceMerger() *InvoiceMerger {
	return &InvoiceMerger{
		uploadDir: "./uploads",
		outputDir: "./output",
	}
}

func (im *InvoiceMerger) ensureDirectories() error {
	dirs := []string{im.uploadDir, im.outputDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}
	return nil
}

func (im *InvoiceMerger) saveUploadedFile(file *multipart.FileHeader) (string, error) {
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 生成唯一文件名
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("%d_%s", timestamp, file.Filename)
	filepath := filepath.Join(im.uploadDir, filename)

	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		return "", err
	}

	return filepath, nil
}

func (im *InvoiceMerger) validatePDFFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("无法打开文件: %v", err)
	}
	defer file.Close()

	// 检查文件大小
	stat, err := file.Stat()
	if err != nil {
		return fmt.Errorf("无法获取文件信息: %v", err)
	}
	if stat.Size() == 0 {
		return fmt.Errorf("文件为空")
	}
	if stat.Size() < 100 {
		return fmt.Errorf("文件太小，可能不是有效的PDF")
	}

	// 检查PDF头部
	header := make([]byte, 8)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("无法读取文件头部: %v", err)
	}

	if !strings.HasPrefix(string(header), "%PDF-") {
		return fmt.Errorf("不是有效的PDF文件")
	}

	return nil
}

func (im *InvoiceMerger) mergePDFs(filePaths []string, layout int) (string, error) {
	// 检查是否有文件
	if len(filePaths) == 0 {
		return "", fmt.Errorf("没有文件需要合并")
	}

	// 验证所有PDF文件
	validFiles := []string{}
	for _, filePath := range filePaths {
		if err := im.validatePDFFile(filePath); err != nil {
			log.Printf("警告：跳过无效文件 %s: %v", filepath.Base(filePath), err)
			continue
		}
		validFiles = append(validFiles, filePath)
	}

	if len(validFiles) == 0 {
		return "", fmt.Errorf("没有有效的PDF文件")
	}

	// 生成输出文件名
	timestamp := time.Now().Unix()
	outputPath := filepath.Join(im.outputDir, fmt.Sprintf("merged_%d_layout_%d.pdf", timestamp, layout))

	// 如果只有一个文件，直接复制
	if len(validFiles) == 1 {
		return im.copyFile(validFiles[0], outputPath)
	}

	// 尝试使用专业工具合并
	if err := im.tryPDFTK(validFiles, outputPath); err == nil {
		log.Printf("✅ 使用pdftk成功合并 %d 个PDF文件", len(validFiles))
		return outputPath, nil
	}

	if err := im.tryGhostscript(validFiles, outputPath); err == nil {
		log.Printf("✅ 使用ghostscript成功合并 %d 个PDF文件", len(validFiles))
		return outputPath, nil
	}

	// 如果都失败，返回错误
	return "", fmt.Errorf("PDF合并失败：无法使用任何可用工具合并文件")
}

func (im *InvoiceMerger) copyFile(src, dst string) (string, error) {
	sourceFile, err := os.Open(src)
	if err != nil {
		return "", fmt.Errorf("打开源文件失败: %v", err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return "", fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return "", fmt.Errorf("复制文件失败: %v", err)
	}

	log.Printf("✅ 单文件处理完成: %s", dst)
	return dst, nil
}

func (im *InvoiceMerger) tryPDFTK(filePaths []string, outputPath string) error {
	// 检查pdftk是否可用
	if _, err := exec.LookPath("pdftk"); err != nil {
		return fmt.Errorf("pdftk不可用")
	}

	// 构建pdftk命令
	args := append(filePaths, "cat", "output", outputPath)
	cmd := exec.Command("pdftk", args...)
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("pdftk执行失败: %v, 输出: %s", err, string(output))
	}

	// 检查输出文件是否存在且有效
	if err := im.validatePDFFile(outputPath); err != nil {
		return fmt.Errorf("pdftk生成的文件无效: %v", err)
	}

	return nil
}

func (im *InvoiceMerger) tryGhostscript(filePaths []string, outputPath string) error {
	// 检查gs是否可用
	if _, err := exec.LookPath("gs"); err != nil {
		return fmt.Errorf("ghostscript不可用")
	}

	// 构建ghostscript命令，使用更好的参数
	args := []string{
		"-dNOPAUSE",
		"-dBATCH",
		"-dSAFER",
		"-sDEVICE=pdfwrite",
		"-dCompatibilityLevel=1.4",
		"-dPDFSETTINGS=/default",
		"-sOutputFile=" + outputPath,
	}
	args = append(args, filePaths...)

	cmd := exec.Command("gs", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ghostscript执行失败: %v, 输出: %s", err, string(output))
	}

	// 检查输出文件是否存在且有效
	if err := im.validatePDFFile(outputPath); err != nil {
		return fmt.Errorf("ghostscript生成的文件无效: %v", err)
	}

	return nil
}

func (im *InvoiceMerger) uploadHandler(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有上传文件"})
		return
	}

	layout, err := strconv.Atoi(c.DefaultPostForm("layout", "2"))
	if err != nil || layout < 1 || layout > 4 {
		layout = 2
	}

	var filePaths []string
	var fileNames []string
	
	for _, file := range files {
		// 检查文件类型
		if !strings.HasSuffix(strings.ToLower(file.Filename), ".pdf") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "只支持PDF文件"})
			return
		}

		filePath, err := im.saveUploadedFile(file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件失败"})
			return
		}
		filePaths = append(filePaths, filePath)
		fileNames = append(fileNames, file.Filename)
	}

	log.Printf("📁 开始处理 %d 个PDF文件: %v", len(filePaths), fileNames)

	// 合并PDF
	outputPath, err := im.mergePDFs(filePaths, layout)
	if err != nil {
		// 清理上传的文件
		for _, filePath := range filePaths {
			os.Remove(filePath)
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "合并PDF失败: " + err.Error()})
		return
	}

	// 清理上传的文件
	for _, filePath := range filePaths {
		os.Remove(filePath)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"downloadUrl": "/download/" + filepath.Base(outputPath),
		"message":     fmt.Sprintf("✅ 成功合并 %d 个PDF文件", len(fileNames)),
		"files":       fileNames,
	})
}

func (im *InvoiceMerger) downloadHandler(c *gin.Context) {
	filename := c.Param("filename")
	filePath := filepath.Join(im.outputDir, filename)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/pdf")
	c.File(filePath)

	// 下载后删除文件
	go func() {
		time.Sleep(5 * time.Minute)
		os.Remove(filePath)
	}()
}

func main() {
	merger := NewInvoiceMerger()
	if err := merger.ensureDirectories(); err != nil {
		log.Fatal("创建目录失败:", err)
	}

	// 检查可用的PDF工具
	log.Println("🔍 检查PDF合并工具...")
	if _, err := exec.LookPath("pdftk"); err == nil {
		log.Println("✅ 找到pdftk，将优先使用专业PDF合并")
	} else {
		log.Println("⚠️ 未找到pdftk")
	}
	
	if _, err := exec.LookPath("gs"); err == nil {
		log.Println("✅ 找到ghostscript，可用作备选PDF合并工具")
	} else {
		log.Println("⚠️ 未找到ghostscript")
	}

	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	// 路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "PDF电子发票批量合并打印",
		})
	})

	r.POST("/upload", merger.uploadHandler)
	r.GET("/download/:filename", merger.downloadHandler)

	fmt.Println("🚀 服务器启动在 http://localhost:8085")
	fmt.Println("📄 支持专业PDF合并功能，包含完整错误处理！")
	r.Run(":8085")
}
