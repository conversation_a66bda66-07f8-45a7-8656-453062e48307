package main

import (
	"fmt"
	"log"
	"os"

	"github.com/pdfcpu/pdfcpu/pkg/api"
)

func main() {
	// 测试pdfcpu合并功能
	fmt.Println("测试pdfcpu PDF合并功能...")

	// 检查是否有现有的PDF文件
	testFiles := []string{}
	
	// 查找现有的PDF文件
	files, err := os.ReadDir(".")
	if err != nil {
		log.Fatal("读取目录失败:", err)
	}

	for _, file := range files {
		if !file.IsDir() && len(file.Name()) > 4 && file.Name()[len(file.Name())-4:] == ".pdf" {
			testFiles = append(testFiles, file.Name())
			if len(testFiles) >= 2 {
				break // 只需要2个文件进行测试
			}
		}
	}

	if len(testFiles) == 0 {
		fmt.Println("没有找到PDF文件进行测试")
		return
	}

	fmt.Printf("找到 %d 个PDF文件: %v\n", len(testFiles), testFiles)

	// 测试合并
	outputFile := "test_merged.pdf"
	
	fmt.Printf("正在合并文件到: %s\n", outputFile)
	err = api.MergeCreateFile(testFiles, outputFile, false, nil)
	if err != nil {
		log.Printf("合并失败: %v", err)
		return
	}

	// 检查输出文件
	if _, err := os.Stat(outputFile); os.IsNotExist(err) {
		fmt.Println("❌ 合并后的文件未生成")
		return
	}

	fmt.Println("✅ PDF合并测试成功！")
	fmt.Printf("输出文件: %s\n", outputFile)
}
