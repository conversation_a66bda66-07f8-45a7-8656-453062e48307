<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试上传功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #4CAF50;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            background-color: #f9f9f9;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin: 20px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>PDF发票合并工具 - 测试页面</h1>
    
    <div class="test-section">
        <h2>功能测试</h2>
        <p>由于UniPDF需要许可证，我们可以测试以下功能：</p>
        
        <div class="upload-area" onclick="document.getElementById('testFile').click()">
            <p>点击这里选择PDF文件进行测试</p>
            <input type="file" id="testFile" multiple accept=".pdf" style="display: none;">
        </div>
        
        <div>
            <label>选择布局：</label>
            <button onclick="setLayout(2)" id="layout2" class="active">每页2张</button>
            <button onclick="setLayout(3)" id="layout3">每页3张</button>
            <button onclick="setLayout(4)" id="layout4">每页4张</button>
        </div>
        
        <button onclick="testUpload()">测试上传</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>服务器状态检查</h2>
        <button onclick="checkServer()">检查服务器状态</button>
        <div id="serverStatus" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>使用说明</h2>
        <ol>
            <li>确保服务器在 http://localhost:8082 运行</li>
            <li>选择一个或多个PDF文件</li>
            <li>选择合并布局（每页2张、3张或4张）</li>
            <li>点击测试上传按钮</li>
            <li>✅ <strong>现在使用开源pdfcpu库，无需许可证！</strong></li>
        </ol>

        <h3>✅ 新版本优势：</h3>
        <ul>
            <li>✅ 使用开源pdfcpu库，完全免费</li>
            <li>✅ 无需任何许可证或注册</li>
            <li>✅ PDF合并功能完全可用</li>
            <li>✅ 支持所有PDF文件格式</li>
        </ul>
    </div>

    <script>
        let currentLayout = 2;
        let selectedFiles = [];

        function setLayout(layout) {
            currentLayout = layout;
            document.querySelectorAll('button[id^="layout"]').forEach(btn => {
                btn.style.background = '#4CAF50';
            });
            document.getElementById('layout' + layout).style.background = '#45a049';
        }

        document.getElementById('testFile').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = `<h3>已选择 ${selectedFiles.length} 个文件：</h3>` +
                selectedFiles.map(f => `<p>- ${f.name} (${(f.size/1024/1024).toFixed(2)} MB)</p>`).join('');
        });

        async function testUpload() {
            if (selectedFiles.length === 0) {
                alert('请先选择PDF文件');
                return;
            }

            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });
            formData.append('layout', currentLayout.toString());

            const result = document.getElementById('result');
            result.innerHTML = '<p>正在上传和处理...</p>';

            try {
                const response = await fetch('http://localhost:8082/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <h3>✅ 上传成功！</h3>
                        <p>文件已合并，布局：每页${currentLayout}张</p>
                        <a href="http://localhost:8082${data.downloadUrl}" target="_blank">
                            <button>下载合并后的PDF</button>
                        </a>
                    `;
                } else {
                    result.innerHTML = `
                        <h3>❌ 处理失败</h3>
                        <p>错误信息：${data.error}</p>
                        <p>这可能是因为缺少UniPDF许可证</p>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <h3>❌ 网络错误</h3>
                    <p>无法连接到服务器：${error.message}</p>
                    <p>请确保服务器在 http://localhost:8082 运行</p>
                `;
            }
        }

        async function checkServer() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = '<p>检查中...</p>';

            try {
                const response = await fetch('http://localhost:8082/');
                if (response.ok) {
                    statusDiv.innerHTML = '<h3>✅ 服务器运行正常</h3><p>可以进行文件上传测试</p>';
                } else {
                    statusDiv.innerHTML = '<h3>⚠️ 服务器响应异常</h3><p>状态码：' + response.status + '</p>';
                }
            } catch (error) {
                statusDiv.innerHTML = '<h3>❌ 无法连接服务器</h3><p>请确保服务器在 http://localhost:8082 运行</p>';
            }
        }

        // 页面加载时自动检查服务器状态
        window.onload = function() {
            checkServer();
        };
    </script>
</body>
</html>
