package main

import (
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/unidoc/unipdf/v3/model"
)

type InvoiceMerger struct {
	uploadDir string
	outputDir string
}

func NewInvoiceMerger() *InvoiceMerger {
	return &InvoiceMerger{
		uploadDir: "./uploads",
		outputDir: "./output",
	}
}

func (im *InvoiceMerger) ensureDirectories() error {
	dirs := []string{im.uploadDir, im.outputDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}
	return nil
}

func (im *InvoiceMerger) saveUploadedFile(file *multipart.FileHeader) (string, error) {
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 生成唯一文件名
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("%d_%s", timestamp, file.Filename)
	filepath := filepath.Join(im.uploadDir, filename)

	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		return "", err
	}

	return filepath, nil
}

func (im *InvoiceMerger) mergePDFs(filePaths []string, layout int) (string, error) {
	// 检查是否有文件
	if len(filePaths) == 0 {
		return "", fmt.Errorf("没有文件需要合并")
	}

	// 创建新的PDF写入器
	pdfWriter := model.NewPdfWriter()

	successCount := 0
	for _, filePath := range filePaths {
		// 读取PDF文件
		pdfReader, file, err := model.NewPdfReaderFromFile(filePath, nil)
		if err != nil {
			log.Printf("Error reading PDF %s: %v", filePath, err)
			// 检查是否是许可证错误
			if strings.Contains(err.Error(), "license") {
				return "", fmt.Errorf("UniPDF许可证错误：需要有效的许可证才能处理PDF文件。请访问 https://unidoc.io 获取许可证")
			}
			continue
		}

		numPages, err := pdfReader.GetNumPages()
		if err != nil {
			file.Close()
			log.Printf("Error getting page count for %s: %v", filePath, err)
			continue
		}

		// 添加所有页面到新PDF
		for pageNum := 1; pageNum <= numPages; pageNum++ {
			page, err := pdfReader.GetPage(pageNum)
			if err != nil {
				log.Printf("Error getting page %d from %s: %v", pageNum, filePath, err)
				continue
			}

			err = pdfWriter.AddPage(page)
			if err != nil {
				log.Printf("Error adding page to writer: %v", err)
				continue
			}
		}

		file.Close()
		successCount++
	}

	if successCount == 0 {
		return "", fmt.Errorf("没有成功处理任何PDF文件")
	}

	// 生成输出文件名
	timestamp := time.Now().Unix()
	outputPath := filepath.Join(im.outputDir, fmt.Sprintf("merged_%d_layout_%d.pdf", timestamp, layout))

	// 写入文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	err = pdfWriter.Write(outputFile)
	if err != nil {
		return "", fmt.Errorf("写入PDF文件失败: %v", err)
	}

	log.Printf("成功合并 %d 个PDF文件到 %s", successCount, outputPath)
	return outputPath, nil
}

func (im *InvoiceMerger) uploadHandler(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有上传文件"})
		return
	}

	layout, err := strconv.Atoi(c.DefaultPostForm("layout", "2"))
	if err != nil || layout < 1 || layout > 4 {
		layout = 2
	}

	var filePaths []string
	for _, file := range files {
		// 检查文件类型
		if !strings.HasSuffix(strings.ToLower(file.Filename), ".pdf") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "只支持PDF文件"})
			return
		}

		filePath, err := im.saveUploadedFile(file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件失败"})
			return
		}
		filePaths = append(filePaths, filePath)
	}

	// 合并PDF
	outputPath, err := im.mergePDFs(filePaths, layout)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "合并PDF失败: " + err.Error()})
		return
	}

	// 清理上传的文件
	for _, filePath := range filePaths {
		os.Remove(filePath)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"downloadUrl": "/download/" + filepath.Base(outputPath),
	})
}

func (im *InvoiceMerger) downloadHandler(c *gin.Context) {
	filename := c.Param("filename")
	filePath := filepath.Join(im.outputDir, filename)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/pdf")
	c.File(filePath)

	// 下载后删除文件
	go func() {
		time.Sleep(5 * time.Minute)
		os.Remove(filePath)
	}()
}

func main() {
	merger := NewInvoiceMerger()
	if err := merger.ensureDirectories(); err != nil {
		log.Fatal("创建目录失败:", err)
	}

	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	// 路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "PDF电子发票批量合并打印",
		})
	})

	r.POST("/upload", merger.uploadHandler)
	r.GET("/download/:filename", merger.downloadHandler)

	fmt.Println("服务器启动在 http://localhost:8081")
	r.Run(":8081")
}
