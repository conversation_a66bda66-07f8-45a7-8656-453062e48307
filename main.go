package main

import (
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type InvoiceMerger struct {
	uploadDir string
	outputDir string
}

func NewInvoiceMerger() *InvoiceMerger {
	return &InvoiceMerger{
		uploadDir: "./uploads",
		outputDir: "./output",
	}
}

func (im *InvoiceMerger) ensureDirectories() error {
	dirs := []string{im.uploadDir, im.outputDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}
	return nil
}

func (im *InvoiceMerger) saveUploadedFile(file *multipart.FileHeader) (string, error) {
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("%d_%s", timestamp, file.Filename)
	filepath := filepath.Join(im.uploadDir, filename)

	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		return "", err
	}

	return filepath, nil
}

func (im *InvoiceMerger) validatePDFFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("无法打开文件: %v", err)
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return fmt.Errorf("无法获取文件信息: %v", err)
	}
	if stat.Size() < 100 {
		return fmt.Errorf("文件太小，可能不是有效的PDF")
	}

	header := make([]byte, 8)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("无法读取文件头部: %v", err)
	}

	if !strings.HasPrefix(string(header), "%PDF-") {
		return fmt.Errorf("不是有效的PDF文件")
	}

	return nil
}

// 创建真正的页面布局合并
func (im *InvoiceMerger) createLayoutMerge(filePaths []string, layout int) (string, error) {
	if len(filePaths) == 0 {
		return "", fmt.Errorf("没有文件需要合并")
	}

	// 验证所有PDF文件
	validFiles := []string{}
	for _, filePath := range filePaths {
		if err := im.validatePDFFile(filePath); err != nil {
			log.Printf("警告：跳过无效文件 %s: %v", filepath.Base(filePath), err)
			continue
		}
		validFiles = append(validFiles, filePath)
	}

	if len(validFiles) == 0 {
		return "", fmt.Errorf("没有有效的PDF文件")
	}

	timestamp := time.Now().Unix()
	outputPath := filepath.Join(im.outputDir, fmt.Sprintf("merged_%d_layout_%d.pdf", timestamp, layout))

	// 如果只有一个文件，直接复制
	if len(validFiles) == 1 {
		return im.copyFile(validFiles[0], outputPath)
	}

	// 尝试使用LaTeX/pdflatex创建真正的布局
	if err := im.createLatexLayout(validFiles, outputPath, layout); err == nil {
		log.Printf("✅ 使用LaTeX成功创建布局合并 %d 个PDF文件", len(validFiles))
		return outputPath, nil
	}

	// 如果LaTeX不可用，使用pdftk简单合并
	if err := im.tryPDFTK(validFiles, outputPath); err == nil {
		log.Printf("✅ 使用pdftk成功合并 %d 个PDF文件（简单合并）", len(validFiles))
		return outputPath, nil
	}

	// 最后使用ghostscript
	if err := im.tryGhostscript(validFiles, outputPath); err == nil {
		log.Printf("✅ 使用ghostscript成功合并 %d 个PDF文件（简单合并）", len(validFiles))
		return outputPath, nil
	}

	return "", fmt.Errorf("PDF合并失败：无法使用任何可用工具合并文件")
}

// 使用LaTeX创建真正的页面布局
func (im *InvoiceMerger) createLatexLayout(filePaths []string, outputPath string, layout int) error {
	// 检查pdflatex是否可用
	if _, err := exec.LookPath("pdflatex"); err != nil {
		return fmt.Errorf("pdflatex不可用")
	}

	// 创建临时目录
	tempDir := filepath.Join(im.outputDir, "temp_latex")
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// 创建LaTeX文档
	texFile := filepath.Join(tempDir, "layout.tex")
	if err := im.createLatexDocument(texFile, filePaths, layout); err != nil {
		return fmt.Errorf("创建LaTeX文档失败: %v", err)
	}

	// 编译LaTeX
	cmd := exec.Command("pdflatex", "-output-directory", tempDir, texFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("LaTeX编译失败: %v, 输出: %s", err, string(output))
	}

	// 移动生成的PDF到目标位置
	generatedPDF := filepath.Join(tempDir, "layout.pdf")
	if err := os.Rename(generatedPDF, outputPath); err != nil {
		return fmt.Errorf("移动PDF文件失败: %v", err)
	}

	return nil
}

func (im *InvoiceMerger) createLatexDocument(texFile string, filePaths []string, layout int) error {
	file, err := os.Create(texFile)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入LaTeX文档头部
	fmt.Fprintf(file, `\documentclass[a4paper]{article}
\usepackage[margin=1cm]{geometry}
\usepackage{graphicx}
\usepackage{pdfpages}
\pagestyle{empty}

\begin{document}
`)

	// 根据布局生成页面
	switch layout {
	case 2:
		// 每页2张，上下排列
		for i := 0; i < len(filePaths); i += 2 {
			fmt.Fprintf(file, `\begin{figure}[p]
\centering
\includegraphics[width=\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i])
			if i+1 < len(filePaths) {
				fmt.Fprintf(file, `\vfill
\includegraphics[width=\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i+1])
			}
			fmt.Fprintf(file, `\end{figure}
\clearpage
`)
		}
	case 3:
		// 每页3张，2上1下
		for i := 0; i < len(filePaths); i += 3 {
			fmt.Fprintf(file, `\begin{figure}[p]
\centering
\includegraphics[width=0.48\textwidth,height=0.3\textheight,keepaspectratio]{%s}
`, filePaths[i])
			if i+1 < len(filePaths) {
				fmt.Fprintf(file, `\hfill
\includegraphics[width=0.48\textwidth,height=0.3\textheight,keepaspectratio]{%s}
`, filePaths[i+1])
			}
			if i+2 < len(filePaths) {
				fmt.Fprintf(file, `\vfill
\includegraphics[width=\textwidth,height=0.3\textheight,keepaspectratio]{%s}
`, filePaths[i+2])
			}
			fmt.Fprintf(file, `\end{figure}
\clearpage
`)
		}
	case 4:
		// 每页4张，2x2网格
		for i := 0; i < len(filePaths); i += 4 {
			fmt.Fprintf(file, `\begin{figure}[p]
\centering
\includegraphics[width=0.48\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i])
			if i+1 < len(filePaths) {
				fmt.Fprintf(file, `\hfill
\includegraphics[width=0.48\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i+1])
			}
			fmt.Fprintf(file, `\vfill
`)
			if i+2 < len(filePaths) {
				fmt.Fprintf(file, `\includegraphics[width=0.48\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i+2])
			}
			if i+3 < len(filePaths) {
				fmt.Fprintf(file, `\hfill
\includegraphics[width=0.48\textwidth,height=0.45\textheight,keepaspectratio]{%s}
`, filePaths[i+3])
			}
			fmt.Fprintf(file, `\end{figure}
\clearpage
`)
		}
	}

	fmt.Fprintf(file, `\end{document}`)
	return nil
}

func (im *InvoiceMerger) copyFile(src, dst string) (string, error) {
	sourceFile, err := os.Open(src)
	if err != nil {
		return "", fmt.Errorf("打开源文件失败: %v", err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return "", fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return "", fmt.Errorf("复制文件失败: %v", err)
	}

	log.Printf("✅ 单文件处理完成: %s", dst)
	return dst, nil
}

func (im *InvoiceMerger) tryPDFTK(filePaths []string, outputPath string) error {
	if _, err := exec.LookPath("pdftk"); err != nil {
		return fmt.Errorf("pdftk不可用")
	}

	args := append(filePaths, "cat", "output", outputPath)
	cmd := exec.Command("pdftk", args...)
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("pdftk执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

func (im *InvoiceMerger) tryGhostscript(filePaths []string, outputPath string) error {
	if _, err := exec.LookPath("gs"); err != nil {
		return fmt.Errorf("ghostscript不可用")
	}

	args := []string{
		"-dNOPAUSE",
		"-dBATCH",
		"-dSAFER",
		"-sDEVICE=pdfwrite",
		"-dCompatibilityLevel=1.4",
		"-dPDFSETTINGS=/default",
		"-sOutputFile=" + outputPath,
	}
	args = append(args, filePaths...)

	cmd := exec.Command("gs", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ghostscript执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

func (im *InvoiceMerger) uploadHandler(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有上传文件"})
		return
	}

	layout, err := strconv.Atoi(c.DefaultPostForm("layout", "2"))
	if err != nil || layout < 1 || layout > 4 {
		layout = 2
	}

	var filePaths []string
	var fileNames []string
	
	for _, file := range files {
		if !strings.HasSuffix(strings.ToLower(file.Filename), ".pdf") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "只支持PDF文件"})
			return
		}

		filePath, err := im.saveUploadedFile(file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件失败"})
			return
		}
		filePaths = append(filePaths, filePath)
		fileNames = append(fileNames, file.Filename)
	}

	log.Printf("📁 开始创建布局合并 %d 个PDF文件 (每页%d张): %v", len(filePaths), layout, fileNames)

	// 创建布局合并
	outputPath, err := im.createLayoutMerge(filePaths, layout)
	if err != nil {
		for _, filePath := range filePaths {
			os.Remove(filePath)
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建布局合并失败: " + err.Error()})
		return
	}

	// 清理上传的文件
	for _, filePath := range filePaths {
		os.Remove(filePath)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"downloadUrl": "/download/" + filepath.Base(outputPath),
		"message":     fmt.Sprintf("✅ 成功创建每页%d张的布局合并，包含 %d 个PDF文件", layout, len(fileNames)),
		"files":       fileNames,
		"layout":      layout,
	})
}

func (im *InvoiceMerger) downloadHandler(c *gin.Context) {
	filename := c.Param("filename")
	filePath := filepath.Join(im.outputDir, filename)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/pdf")
	c.File(filePath)

	go func() {
		time.Sleep(5 * time.Minute)
		os.Remove(filePath)
	}()
}

func main() {
	merger := NewInvoiceMerger()
	if err := merger.ensureDirectories(); err != nil {
		log.Fatal("创建目录失败:", err)
	}

	log.Println("🔍 检查PDF布局工具...")
	if _, err := exec.LookPath("pdflatex"); err == nil {
		log.Println("✅ 找到pdflatex，将使用LaTeX创建真正的页面布局")
	} else {
		log.Println("⚠️ 未找到pdflatex，将使用简单合并")
	}
	
	if _, err := exec.LookPath("pdftk"); err == nil {
		log.Println("✅ 找到pdftk，可用于PDF合并")
	}
	
	if _, err := exec.LookPath("gs"); err == nil {
		log.Println("✅ 找到ghostscript，可用于PDF合并")
	}

	r := gin.Default()
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "PDF电子发票批量合并打印",
		})
	})

	r.POST("/upload", merger.uploadHandler)
	r.GET("/download/:filename", merger.downloadHandler)

	fmt.Println("🚀 服务器启动在 http://localhost:8086")
	fmt.Println("📄 支持真正的页面布局合并功能！")
	fmt.Println("💡 每页2/3/4张发票的真实布局实现")
	r.Run(":8086")
}
