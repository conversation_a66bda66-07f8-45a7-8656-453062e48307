package main

import (
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type InvoiceMerger struct {
	uploadDir string
	outputDir string
}

func NewInvoiceMerger() *InvoiceMerger {
	return &InvoiceMerger{
		uploadDir: "./uploads",
		outputDir: "./output",
	}
}

func (im *InvoiceMerger) ensureDirectories() error {
	dirs := []string{im.uploadDir, im.outputDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}
	return nil
}

func (im *InvoiceMerger) saveUploadedFile(file *multipart.FileHeader) (string, error) {
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 生成唯一文件名
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("%d_%s", timestamp, file.Filename)
	filepath := filepath.Join(im.uploadDir, filename)

	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	if _, err = io.Copy(dst, src); err != nil {
		return "", err
	}

	return filepath, nil
}

func (im *InvoiceMerger) mergePDFsWithCommand(filePaths []string, layout int) (string, error) {
	// 检查是否有文件
	if len(filePaths) == 0 {
		return "", fmt.Errorf("没有文件需要合并")
	}

	// 生成输出文件名
	timestamp := time.Now().Unix()
	outputPath := filepath.Join(im.outputDir, fmt.Sprintf("merged_%d_layout_%d.pdf", timestamp, layout))

	// 如果只有一个文件，直接复制
	if len(filePaths) == 1 {
		sourceFile, err := os.Open(filePaths[0])
		if err != nil {
			return "", fmt.Errorf("打开源文件失败: %v", err)
		}
		defer sourceFile.Close()

		destFile, err := os.Create(outputPath)
		if err != nil {
			return "", fmt.Errorf("创建目标文件失败: %v", err)
		}
		defer destFile.Close()

		_, err = io.Copy(destFile, sourceFile)
		if err != nil {
			return "", fmt.Errorf("复制文件失败: %v", err)
		}

		log.Printf("单文件处理完成: %s", outputPath)
		return outputPath, nil
	}

	// 尝试使用系统命令合并PDF
	// 首先尝试使用 pdftk（如果可用）
	if im.tryPDFTK(filePaths, outputPath) {
		log.Printf("使用pdftk成功合并 %d 个PDF文件", len(filePaths))
		return outputPath, nil
	}

	// 如果pdftk不可用，尝试使用 ghostscript
	if im.tryGhostscript(filePaths, outputPath) {
		log.Printf("使用ghostscript成功合并 %d 个PDF文件", len(filePaths))
		return outputPath, nil
	}

	// 如果都不可用，使用简单的文件连接作为后备方案
	log.Println("警告：无法找到PDF合并工具，使用简单连接方式")
	return im.simpleConcatenate(filePaths, outputPath)
}

func (im *InvoiceMerger) tryPDFTK(filePaths []string, outputPath string) bool {
	// 检查pdftk是否可用
	if _, err := exec.LookPath("pdftk"); err != nil {
		return false
	}

	// 构建pdftk命令
	args := append(filePaths, "cat", "output", outputPath)
	cmd := exec.Command("pdftk", args...)
	
	err := cmd.Run()
	if err != nil {
		log.Printf("pdftk执行失败: %v", err)
		return false
	}

	// 检查输出文件是否存在
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		return false
	}

	return true
}

func (im *InvoiceMerger) tryGhostscript(filePaths []string, outputPath string) bool {
	// 检查gs是否可用
	if _, err := exec.LookPath("gs"); err != nil {
		return false
	}

	// 构建ghostscript命令
	args := []string{
		"-dNOPAUSE",
		"-dBATCH",
		"-sDEVICE=pdfwrite",
		"-sOutputFile=" + outputPath,
	}
	args = append(args, filePaths...)

	cmd := exec.Command("gs", args...)
	err := cmd.Run()
	if err != nil {
		log.Printf("ghostscript执行失败: %v", err)
		return false
	}

	// 检查输出文件是否存在
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		return false
	}

	return true
}

func (im *InvoiceMerger) simpleConcatenate(filePaths []string, outputPath string) (string, error) {
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	// 写入所有文件的内容
	for i, filePath := range filePaths {
		inputFile, err := os.Open(filePath)
		if err != nil {
			log.Printf("警告：无法打开文件 %s: %v", filePath, err)
			continue
		}

		_, err = io.Copy(outputFile, inputFile)
		inputFile.Close()
		
		if err != nil {
			log.Printf("警告：复制文件 %s 失败: %v", filePath, err)
			continue
		}

		log.Printf("已添加文件 %d/%d: %s", i+1, len(filePaths), filepath.Base(filePath))
	}

	log.Printf("简单连接完成，包含 %d 个文件", len(filePaths))
	return outputPath, nil
}

func (im *InvoiceMerger) uploadHandler(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法解析上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有上传文件"})
		return
	}

	layout, err := strconv.Atoi(c.DefaultPostForm("layout", "2"))
	if err != nil || layout < 1 || layout > 4 {
		layout = 2
	}

	var filePaths []string
	for _, file := range files {
		// 检查文件类型
		if !strings.HasSuffix(strings.ToLower(file.Filename), ".pdf") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "只支持PDF文件"})
			return
		}

		filePath, err := im.saveUploadedFile(file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件失败"})
			return
		}
		filePaths = append(filePaths, filePath)
	}

	log.Printf("开始处理 %d 个PDF文件", len(filePaths))

	// 合并PDF
	outputPath, err := im.mergePDFsWithCommand(filePaths, layout)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "合并PDF失败: " + err.Error()})
		return
	}

	// 清理上传的文件
	for _, filePath := range filePaths {
		os.Remove(filePath)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"downloadUrl": "/download/" + filepath.Base(outputPath),
		"message":     fmt.Sprintf("成功处理 %d 个PDF文件", len(filePaths)),
	})
}

func (im *InvoiceMerger) downloadHandler(c *gin.Context) {
	filename := c.Param("filename")
	filePath := filepath.Join(im.outputDir, filename)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/pdf")
	c.File(filePath)

	// 下载后删除文件
	go func() {
		time.Sleep(5 * time.Minute)
		os.Remove(filePath)
	}()
}

func main() {
	merger := NewInvoiceMerger()
	if err := merger.ensureDirectories(); err != nil {
		log.Fatal("创建目录失败:", err)
	}

	// 检查可用的PDF工具
	log.Println("检查PDF合并工具...")
	if _, err := exec.LookPath("pdftk"); err == nil {
		log.Println("✅ 找到pdftk，将使用专业PDF合并")
	} else if _, err := exec.LookPath("gs"); err == nil {
		log.Println("✅ 找到ghostscript，将使用gs合并PDF")
	} else {
		log.Println("⚠️ 未找到专业PDF工具，将使用简单合并方式")
	}

	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	// 路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "PDF电子发票批量合并打印",
		})
	})

	r.POST("/upload", merger.uploadHandler)
	r.GET("/download/:filename", merger.downloadHandler)

	fmt.Println("服务器启动在 http://localhost:8084")
	fmt.Println("支持真正的PDF合并功能！")
	r.Run(":8084")
}
